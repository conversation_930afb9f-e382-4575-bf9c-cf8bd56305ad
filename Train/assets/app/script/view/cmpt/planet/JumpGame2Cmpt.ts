import { HeroAction, HeroAnimation, PassengerLifeAnimation, PlanetEvent } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import { animHelper } from "../../../common/helper/AnimHelper";
import { gameHelper } from "../../../common/helper/GameHelper";
import { resHelper } from "../../../common/helper/ResHelper";
import ActionTree, { ActionNode } from "../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../model/planet/PlanetEmptyNode";
import PlanetWindCtrl from "../../planet/PlanetWindCtrl";
import HeroCmpt from "../hero/HeroCmpt";
import PlanetNodeCmpt from "./PlanetNodeCmpt";
import PlanetNodeRewardCmpt from "./PlanetNodeRewardCmpt";
import PlanetNodeRewardGroupCmpt from "./PlanetNodeRewardGroupCmpt";

const { ccclass, property } = cc._decorator;


const MAX_TIME = 2
const MAX_DIS = 1000
const MOVE_OFFSET = 200

// 移动停留点的移动类型
enum PointMoveType {
    VERTICAL = "vertical",
    HORIZONTAL = "horizontal"
}

// 移动停留点的状态数据
interface PointMoveData {
    originalX: number      // 原始X位置
    originalY: number      // 原始Y位置
    currentTime: number    // 当前动画时间
    moveType: PointMoveType // 移动类型
    cycleDuration: number  // 移动一个周期的时长（秒）
}

@ccclass
export default class JumpGame2Cmpt extends PlanetNodeCmpt {

    public model: PlanetEmptyNode = null

    private gravity: number = 2000 //重力
    private minY: number = -500 //最小Y 标志着死亡

    private speed: cc.Vec2 = cc.v2()
    private isJump: boolean = false //是否在跳跃中
    private isControl: boolean = false //是否可操作
    private powerSp: cc.Sprite = null    // 跳跃力度

    private touchTime: number = 0

    private points: cc.Node[] = []
    private heroNode: cc.Node = null
    private lineIndex: number = 0
    private prePos: cc.Vec2 = cc.v2()
    private nextPos: cc.Vec2 = cc.v2()
    private intersectPos: cc.Vec2 = cc.v2()
    private reachLines: { start: cc.Vec2, end: cc.Vec2 }[][] = null
    private staticReachLines: { start: cc.Vec2, end: cc.Vec2 }[][] = null
    private movingPoints: Set<number> = new Set()
    // hero相对于移动停留点原始Y的偏移
    private heroYOffsetOnMovingPoint: number = 0
    // hero相对于移动停留点原始X的偏移
    private heroXOffsetOnMovingPoint: number = 0
    private resetPos: cc.Vec2 = null

    // 新增：英雄是否会跳出当前停留点的标志位
    private includeCurrent: boolean = true
    private yOffset: number = 0

    private actionTree: ActionTree = null
    private planetCtrl: PlanetWindCtrl = null

    private rewardNode: cc.Node

    private get curIndex() {
        return this.model.progress
    }
    private set curIndex(val) {
        this.model.progress = val
    }



    public isPointMoving(index: number): boolean {
        return this.movingPoints.has(index)
    }

    private initMovingPoints() {
        this.points.forEach((point, index) => {
            let moveType: PointMoveType | null = null

            if (point.name.endsWith("_move_v")) {
                moveType = PointMoveType.VERTICAL
            } else if (point.name.endsWith("_move_h")) {
                moveType = PointMoveType.HORIZONTAL
            } else if (point.name.endsWith("_move")) {
                // 兼容旧 上下移动
                moveType = PointMoveType.VERTICAL
            }

            if (moveType) {
                this.movingPoints.add(index)
                this.initPointMoveData(point, moveType)
            }
        })
    }

    public listenEventMaps() {
        return [
            { [EventType.PLAENT_CONTROL_TOUCH_START]: this.onTouchStart },
            { [EventType.PLAENT_CONTROL_TOUCH_END]: this.onTouchEnd },
            { [EventType.PLAENT_CONTROL_TOUCH_CANCEL]: this.onTouchEnd },
            { [EventType.PLAENT_CHANGE_JUMP_END]: this.onChange },
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public async init(model: PlanetEmptyNode, planetCtrl) {
        super.init(model)
        this.planetCtrl = planetCtrl
        this.heroNode = planetCtrl.heroNode_

        this.powerSp = this.heroNode.Child('ui/power/bar', cc.Sprite)
        this.points = this.node.Child('points').children

        for (let i = this.curIndex + 1; i < this.points.length; i++) {
            this.points[i].active = false
        }
        this.initMovingPoints()
        this.actionTree = new ActionTree().init(this)

        //摄像机用
        this.model.reachOffset = ut.convertToNodeAR(this.points[0], this.node)
        this.model.endOffset = ut.convertToNodeAR(this.points.last(), this.node)

        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
        this.node.zIndex = 0

        this.initRewards()
    }

    private initRewards() {
        let rewardNode = this.Child("reward")
        this.rewardNode = rewardNode
        if (!rewardNode) return
        this.points.forEach((point, i) => {
            if (!this.model.checkRewardByPoint(i)) return
            if (point.Child(rewardNode.name)) return
            let node = cc.instantiate2(rewardNode, point)
            node.active = true
            node.setPosition(cc.v2(0, point.height / 2))
        })
    }

    private initLines() {
        if (this.staticReachLines) return
        // 只缓存静态停留点的线段
        this.staticReachLines = this.points.map((p, index) => {
            if (this.movingPoints.has(index)) {
                return null // 移动停留点不缓存
            }
            let lineNodes = p.children.filter(c => c.name.includes("line"))
            if (lineNodes.length <= 0 && index == this.points.length - 1) { //最后一个特殊处理
                return [{
                    start: ut.convertToNodeAR(p, this.node.parent, cc.v2(0, 0)),
                    end: ut.convertToNodeAR(p, this.node.parent, cc.v2(10000, 0)),
                }]
            }
            let lines = lineNodes.map(node => {
                let start = ut.convertToNodeAR(node, this.node.parent, cc.v2(-node.anchorX * node.width, 0))
                let end = ut.convertToNodeAR(node, this.node.parent, cc.v2((1 - node.anchorX) * node.width, 0))
                return { start, end }
            })
            return lines
        })

        // 保持向后兼容，reachLines指向staticReachLines
        this.reachLines = this.staticReachLines
    }

    // 实时计算移动停留点的碰撞线段
    private getMovingPointLines(index: number, predictTime?: number) {
        let point = this.points[index]
        let lineNodes = point.children.filter(c => c.name.includes("line"))

        // 如果需要预测位置，临时修改停留点位置进行计算
        let originalX = point.x
        let originalY = point.y

        if (predictTime !== undefined) {
            let predictedPos = this.predictMovingPointPosition(point, predictTime)
            point.x = predictedPos.x
            point.y = predictedPos.y
        }

        let lines: { start: cc.Vec2, end: cc.Vec2 }[]

        if (lineNodes.length <= 0 && index == this.points.length - 1) {
            lines = [{
                start: ut.convertToNodeAR(point, this.node.parent, cc.v2(0, 0)),
                end: ut.convertToNodeAR(point, this.node.parent, cc.v2(10000, 0)),
            }]
        } else {
            lines = lineNodes.map(node => {
                let start = ut.convertToNodeAR(node, this.node.parent, cc.v2(-node.anchorX * node.width, 0))
                let end = ut.convertToNodeAR(node, this.node.parent, cc.v2((1 - node.anchorX) * node.width, 0))
                return { start, end }
            })
        }

        // 恢复停留点原始位置
        if (predictTime !== undefined) {
            point.x = originalX
            point.y = originalY
        }

        return lines
    }

    // 预测移动停留点在指定时间的位置
    private predictMovingPointPosition(point: cc.Node, deltaTime: number): cc.Vec2 {
        const moveData: PointMoveData = point.Data
        if (!moveData) return cc.v2(point.x, point.y)

        // 计算预测时间点的位置
        const futureTime = moveData.currentTime + deltaTime
        const cycleProgress = (futureTime % moveData.cycleDuration) / moveData.cycleDuration
        const sinValue = Math.sin(cycleProgress * Math.PI * 2)

        let predictedX = point.x
        let predictedY = point.y

        switch (moveData.moveType) {
            case PointMoveType.VERTICAL:
                predictedY = moveData.originalY + sinValue * MOVE_OFFSET
                break
            case PointMoveType.HORIZONTAL:
                predictedX = moveData.originalX + sinValue * MOVE_OFFSET
                break
        }

        return cc.v2(predictedX, predictedY)
    }

    // 获取停留点的实时碰撞线段
    private getPointLines(index: number, predictTime?: number) {
        if (this.movingPoints.has(index)) {
            return this.getMovingPointLines(index, predictTime) // 实时计算，支持预测
        } else {
            return this.staticReachLines[index] // 使用缓存
        }
    }

    private async onTarget(model) {
        if (this.model != model) return
        let reachNode = this.points[0]
        if (!reachNode) return
        this.planetCtrl.focusHero()
        let hero = gameHelper.hero
        let pos = this.getFudaoCenter(this.curIndex)
        if (this.curIndex == 0) {
            await this.actionTree.start(async (action: ActionNode) => {
                await action.run(hero.moveToPos, pos, hero)
                action.ok()
            })
        } else {
            hero.setPosition(pos)
            // 如果当前停留点是移动的，计算偏移量
            if (this.isPointMoving(this.curIndex)) {
                const movingPoint = this.points[this.curIndex]
                const moveData: PointMoveData = movingPoint.Data

                if (moveData.moveType === PointMoveType.VERTICAL) {
                    this.heroYOffsetOnMovingPoint = pos.y - movingPoint.y
                    this.heroXOffsetOnMovingPoint = 0
                } else if (moveData.moveType === PointMoveType.HORIZONTAL) {
                    this.heroXOffsetOnMovingPoint = pos.x - movingPoint.x
                    this.heroYOffsetOnMovingPoint = 0
                }
            }
        }
        this.planetCtrl.focusHero({ back: true })
        hero.setAction(HeroAction.IDLE)
        await this.showPoint(this.curIndex + 1)
        this.setControl(true)
        hero.setAction(HeroAction.JUMP_GAME)
    }

    private getFudaoCenter(index) {
        let node = this.points[index]
        let line = node.children.find(c => c.name.includes("line"))
        let width = node.width * node.scaleX
        let height = node.height * node.scaleY
        let startPos = this.node.getPosition()
        let pointPos = node.getPosition()
        let center = ut.convertToNodeAR(line, this.node)
        return cc.v2(startPos.x + center.x, startPos.y + center.y)
        // return cc.v2(startPos.x + pointPos.x + (0.5 - node.anchorX) * width, startPos.y + pointPos.y + (1 - node.anchorY) * height + line.y * node.scaleY)
    }

    private onTouchStart(event: cc.Event.EventTouch) {
        if (!this.isControl) return
        this.touchTime = Date.now()
        this.heroNode.Component(HeroCmpt).playAnimation(HeroAnimation.JUMP2, true)
        this.showPower(true)
        gameHelper.hero.setJump()
    }

    private onTouchEnd(event: cc.Event.EventTouch) {
        if (!this.isControl || this.touchTime <= 0) {
            return
        }
        this.prePos = this.heroNode.getPosition(this.prePos)
        this.resetPos = this.heroNode.getPosition(this.resetPos)

        // 按住多少秒
        let time = (Date.now() - this.touchTime) * 0.001
        time = cc.misc.clampf(time, 0, MAX_TIME)

        let dis = time / MAX_TIME * MAX_DIS
        dis = this.clampDis(dis)
        this.setSpeedByDis(dis)
        if (this.isPointMoving(this.curIndex)) {
            let pointVelocity = this.getMovingPointVelocity(this.curIndex)
            // 如果当前停留点向上移动，预计算英雄X方向移动距离
            if (pointVelocity.y > 0) {
                // 跳跃的飞行时间
                let flightTime = 2 * this.speed.y / this.gravity
                // X方向会移动多远
                let heroXDistance = this.speed.x * flightTime
                const point = this.points[this.curIndex]
                let lineNode = point.children.find(c => c.name.includes("line"))
                if (lineNode) {
                    let heroWorldPos = this.heroNode.getPosition()
                    let lineWorldPos = ut.convertToNodeAR(lineNode, this.node.parent)
                    let rx = heroWorldPos.x - lineWorldPos.x
                    let lw = lineNode.width * lineNode.scaleX
                    let halfWidth = lw / 2
                    // 跳跃后位置
                    let finalX = rx + heroXDistance
                    let willJumpOut = Math.abs(finalX) > halfWidth
                    console.log(`停留点向上移动分析:`)
                    console.log(`- 英雄X方向预计移动: ${heroXDistance.toFixed(2)} 像素`)
                    console.log(`- 英雄当前在线上位置: ${rx.toFixed(2)} (线宽: ${lw.toFixed(2)}, 范围: ±${halfWidth.toFixed(2)})`)
                    console.log(`- 跳跃后位置: ${finalX.toFixed(2)}`)
                    console.log(`- 会跳出当前停留点: ${willJumpOut}`)
                    this.includeCurrent = willJumpOut
                }
            }
        }

        this.isJump = true
        this.setControl(false)

        // hero开始跳跃时，清除移动停留点偏移记录
        this.heroYOffsetOnMovingPoint = 0
        this.heroXOffsetOnMovingPoint = 0



        this.touchTime = 0

        this.showPower(false)
        this.heroNode.Component(HeroCmpt).playAnimation(HeroAnimation.JUMP3)
        this.checkReachEnd(dis, this.prePos.x)
    }

    private setSpeedByDis(dis: number) {
        let tan = ut.tan(60)
        this.speed.x = Math.sqrt(this.gravity * dis / (2 * tan))
        this.speed.y = this.speed.x * tan
    }

    // 计算移动停留点的当前速度
    private getMovingPointVelocity(index: number): cc.Vec2 {
        if (!this.movingPoints.has(index)) {
            return cc.v2(0, 0)
        }

        const point = this.points[index]
        const moveData: PointMoveData = point.Data
        if (!moveData) return cc.v2(0, 0)

        // 计算当前时刻的速度（正弦波的导数是余弦波）
        const cycleProgress = (moveData.currentTime % moveData.cycleDuration) / moveData.cycleDuration
        const cosValue = Math.cos(cycleProgress * Math.PI * 2)
        const velocityMagnitude = cosValue * MOVE_OFFSET * (2 * Math.PI / moveData.cycleDuration)

        switch (moveData.moveType) {
            case PointMoveType.VERTICAL:
                return cc.v2(0, velocityMagnitude)
            case PointMoveType.HORIZONTAL:
                return cc.v2(velocityMagnitude, 0)
            default:
                return cc.v2(0, 0)
        }
    }

    private onChange(x: number) {
        this.setSpeedByDis(x - this.heroNode.x)
    }

    private setControl(bol: boolean) {
        this.isControl = bol
        eventCenter.emit(EventType.PLAENT_CAN_CONTROL_JUMP, bol)
    }

    // 回到起跳点
    private reset() {
        this.setControl(true)
        this.isJump = false
        if (this.isMain()) {
            this.heroNode.setPosition(this.resetPos)
        }
        else {
            let pointIndex = this.getBirthPointIndex()
            this.heroNode.setPosition(this.getFudaoCenter(pointIndex))
            this.curIndex = pointIndex
        }
        this.heroNode.Component(HeroCmpt).playAnimation(PassengerLifeAnimation.IDLE, true)
    }

    private isMain() {
        return this.model.eventName == PlanetEvent.JUMP_1 || this.model.eventName == PlanetEvent.JUMP_2
    }

    private getBirthPointIndex() {
        let index = this.points.slice(0, this.curIndex + 1).findIndex(p => p.name.includes("birth"))
        if (index < 0) {
            return 0
        }
        return index
    }

    private updatePos(dt: number) {
        let interactInfo
        this.prePos = this.heroNode.getPosition(this.prePos)

        // 🔥 优化：如果英雄不会跳出当前停留点，使用简化计算
        if (!this.includeCurrent) {
            // 简化模式：直接更新英雄位置，不需要精确的碰撞检测
            this.speed.y -= this.gravity * dt
            this.nextPos.x = this.prePos.x + this.speed.x * dt
            this.nextPos.y = this.prePos.y + this.speed.y * dt

            this.nextPos.y += this.yOffset
            // 🔥 关键：检查是否应该结束跳跃并落地
            if (this.speed.y <= 0) {
                // 英雄开始下降，直接让他落地到正确位置
                console.log(`简化模式：英雄开始下降，直接落地到正确位置`)

                // 计算正确的落地位置：起跳位置 + 总的X移动距离
                let startPos = this.prePos  // 起跳时保存的位置
                let totalFlightTime = 2 * this.speed.y / this.gravity  // 总飞行时间（从起跳开始）
                let totalXDistance = this.speed.x * totalFlightTime  // 总的X方向移动距离

                let correctLandingPos = cc.v2(
                    startPos.x + totalXDistance,
                    this.getFudaoCenter(this.curIndex).y  // Y坐标使用停留点的高度
                )

                // 模拟落地到正确位置
                interactInfo = {
                    index: this.curIndex,
                    lineIndex: this.lineIndex,
                    pos: correctLandingPos
                }
                console.log(`落地位置：起始X: ${startPos.x.toFixed(2)}, 总移动: ${totalXDistance.toFixed(2)}, 最终X: ${correctLandingPos.x.toFixed(2)}`)
            }
        }
        else {
            // 原有的精确计算模式
            let tot = dt
            let accumulatedTime = 0  // 累积的时间，用于预测移动停留点位置

            while (tot) {
                let stepDt = 0.03
                if (tot < stepDt) {
                    stepDt = tot
                }
                tot -= stepDt
                accumulatedTime += stepDt

                this.speed.y -= this.gravity * stepDt
                this.nextPos.x = this.prePos.x + this.speed.x * stepDt
                this.nextPos.y = this.prePos.y + this.speed.y * stepDt
                let nextPos = this.nextPos

                // 传入累积时间，用于预测移动停留点在这个时间点的位置
                interactInfo = this.checkIntersect(nextPos, accumulatedTime)
                if (interactInfo) {
                    break
                }
                this.prePos.x = this.nextPos.x
                this.prePos.y = this.nextPos.y
            }
        }

        // 🔥 在简化模式下，如果没有碰撞，仍然需要更新英雄位置
        let nextPos = this.nextPos
        if (!interactInfo) {
            this.heroNode.setPosition(nextPos)
        }
        else {
            if (!this.includeCurrent) {
                this.includeCurrent = true
                this.yOffset = 0
            }
            this.curIndex = interactInfo.index
            this.lineIndex = interactInfo.lineIndex
            this.isJump = false
            this.heroNode.setPosition(interactInfo.pos)

            // 如果落在移动停留点上，计算并保存hero相对于停留点的偏移
            if (this.isPointMoving(interactInfo.index)) {
                const movingPoint = this.points[interactInfo.index]
                const moveData: PointMoveData = movingPoint.Data

                if (moveData.moveType === PointMoveType.VERTICAL) {
                    // 上下移动：保存Y偏移量
                    this.heroYOffsetOnMovingPoint = interactInfo.pos.y - movingPoint.y
                    this.heroXOffsetOnMovingPoint = 0
                } else if (moveData.moveType === PointMoveType.HORIZONTAL) {
                    // 左右移动：保存X偏移量
                    this.heroXOffsetOnMovingPoint = interactInfo.pos.x - movingPoint.x
                    this.heroYOffsetOnMovingPoint = 0
                }
            }

            let cmpt = this.heroNode.Component(HeroCmpt)
            let p = cmpt.playAnimation(HeroAnimation.JUMP4).then(() => {
                this.heroNode.Component(HeroCmpt).playAnimation(PassengerLifeAnimation.IDLE, true)
            })
            let p2 = this.checkClaimReward()
            this.showPoint(interactInfo.index + 1)
            Promise.all([p, p2]).then(() => {
                this.model.setProgress(this.curIndex)
                if (this.curIndex < this.reachLines.length - 1) {
                    this.setControl(true)
                }
            })
        }
    }

    private async checkClaimReward() {
        let model = this.model
        if (!model.checkRewardByPoint(this.curIndex)) return
        let rewardIndex = model.getRewardIndex(this.curIndex)
        let succ = await model.claimNodeReward(rewardIndex)
        if (succ) {
            this.flyReward(this.model.rewards[rewardIndex])
        }
        return succ
    }

    private async flyReward(reward) {
        let point = this.points[this.curIndex]
        let node = point.Child(this.rewardNode.name)
        ut.convertParent(node, this.planetCtrl["mapNode_"])
        node.addComponent(PlanetNodeRewardCmpt).init(reward)

        let items = [node]
        let go = (items: cc.Node[]) => {
            if (items.length <= 0) return
            let node = items[0]
            let cmpt = node.addComponent(PlanetNodeRewardGroupCmpt)
            return new Promise((r) => {
                cmpt.init(items, r)
                cmpt.fly()
            })
        }
        await Promise.all([go(items)])
    }

    private async showPoint(index) {
        let point = this.points[index]
        if (!point || point.active) return
        point.active = true
        if (!point.name.includes("_move")) {
            point.opacity = 0
            let orgY = point.y
            let offsetY = 10
            point.y -= 80
            await cc.tween(point).to(0.45, { opacity: 255, y: orgY + offsetY }, { easing: cc.easing.sineOut }).to(0.1, { y: orgY }, { easing: cc.easing.sineInOut }).promise()
        }
    }

    private checkIntersect(pos: cc.Vec2, predictTime?: number) {
        this.initLines()
        let intersectPos = this.intersectPos
        for (let i = 0; i < this.points.length; i++) {
            if (!this.points[i].active) continue

            if (i == this.curIndex) {
                const pointVelocity = this.getMovingPointVelocity(this.curIndex)
                if (pointVelocity.y > 0) {
                    continue
                }
            }

            let lines = this.getPointLines(i, predictTime)
            if (!lines) continue
            for (let j = 0; j < lines.length; j++) {
                if (i == this.curIndex && j != this.lineIndex) continue
                let { start, end } = lines[j]
                if (ut.lineLine(this.prePos, pos, start, end, intersectPos)) {
                    if (!intersectPos.fuzzyEquals(this.prePos, 0.01)) {
                        return { index: i, lineIndex: j, pos: intersectPos }
                    }
                }
            }
        }
    }

    private clampDis(dis) {
        this.initLines()
        let eventName = this.model.eventName
        if (eventName == PlanetEvent.JUMP_1 || eventName == PlanetEvent.JUMP_2) return dis
        if (this.curIndex != this.reachLines.length - 2) return dis

        let lines = this.reachLines.last()
        let maxX = lines[0].start.x + 300
        let maxDis = maxX - this.prePos.x
        return Math.min(dis, maxDis)
    }

    private checkReachEnd(dis: number, x: number) {
        this.initLines()
        if (this.curIndex != this.reachLines.length - 2) return
        let lines = this.reachLines.last()
        let start = lines[0].start.x
        if (dis >= start - x) {
            eventCenter.emit(EventType.PLAENT_CAN_JUMP_TO_END)
        }
    }

    private checkDead() {
        if (this.heroNode.y <= this.minY) {
            return true
        }
    }

    // 刷新力度
    private updatePower() {
        if (this.touchTime === 0) {
            return
        }
        let passTime = (Date.now() - this.touchTime) * 0.001
        let per = passTime / MAX_TIME
        if (per > 1) per = 1
        this.powerSp.fillRange = per
    }

    // 力度显示
    private showPower(isShow: boolean) {
        this.powerSp.fillRange = 0
        this.powerSp.node.parent.active = isShow
    }

    update(dt: number) {
        super.update(dt)

        if (!this.model) return
        this.updatePower()
        this.actionTree && this.actionTree.update(dt)

        // 更新移动停留点
        this.updateMovingPoints(dt)

        if (!this.isJump) {
            return
        }

        this.updatePos(dt)

        if (this.checkDead()) {
            this.reset()
            return
        }

        if (this.curIndex >= this.reachLines.length - 1) {
            let model = this.model
            this.enabled = false
            gameHelper.hero.setPosition(this.heroNode.getPosition())
            ut.wait(0.5, this).then(async () => {
                this.planetCtrl.focusHero()
                await model.die()
                model.end()
            })
        }
    }

    protected onDeath(): void {
    }

    // ==================== 移动停留点相关方法 ====================

    // 初始化会移动的停留点的数据
    private initPointMoveData(point: cc.Node, moveType: PointMoveType) {
        const moveData: PointMoveData = {
            originalX: point.x,
            originalY: point.y,
            currentTime: 0,
            moveType: moveType,
            cycleDuration: 2.0
        }
        point.Data = moveData
    }

    // 更新所有移动停留点的位置
    private updateMovingPoints(dt: number) {
        this.movingPoints.forEach(pointIndex => {
            const point = this.points[pointIndex]
            if (point && point.active && point.Data) {
                const pointVelocity = this.getMovingPointVelocity(pointIndex)
                this.updateSinglePointMove(point, dt, pointVelocity)
            }
        })
    }

    // 更新会动的停留点 
    private updateSinglePointMove(point: cc.Node, dt: number, pointVelocity: cc.Vec2) {
        const moveData: PointMoveData = point.Data
        moveData.currentTime += dt
        // 周期进度
        const cycleProgress = (moveData.currentTime % moveData.cycleDuration) / moveData.cycleDuration
        // 平滑移动
        const sinValue = Math.sin(cycleProgress * Math.PI * 2)
        switch (moveData.moveType) {
            case PointMoveType.VERTICAL:
                const newY = moveData.originalY + sinValue * MOVE_OFFSET
                point.y = newY
                if (!this.includeCurrent && pointVelocity.y > 0) {
                    this.yOffset += newY - moveData.originalY
                }
                break
            case PointMoveType.HORIZONTAL:
                const newX = moveData.originalX + sinValue * MOVE_OFFSET
                point.x = newX
                break
        }
        this.updateHeroOnMovingPoint(point)
    }

    // 更新hero在移动停留点上的位置
    private updateHeroOnMovingPoint(movingPoint: cc.Node) {
        if (this.isJump) return
        if (!this.isHeroOnThisMovingPoint(movingPoint)) return

        const moveData: PointMoveData = movingPoint.Data
        const currentHeroPos = this.heroNode.getPosition()
        switch (moveData.moveType) {
            case PointMoveType.VERTICAL:
                const newHeroY = movingPoint.y + this.heroYOffsetOnMovingPoint
                this.heroNode.setPosition(cc.v2(currentHeroPos.x, newHeroY))
                break
            case PointMoveType.HORIZONTAL:
                const newHeroX = movingPoint.x + this.heroXOffsetOnMovingPoint
                this.heroNode.setPosition(cc.v2(newHeroX, currentHeroPos.y))
                break
        }
        gameHelper.hero.setPosition(this.heroNode.getPosition())
    }

    // 判断hero是否在指定的移动停留点上
    private isHeroOnThisMovingPoint(movingPoint: cc.Node): boolean { return this.curIndex === this.points.indexOf(movingPoint) }
}
